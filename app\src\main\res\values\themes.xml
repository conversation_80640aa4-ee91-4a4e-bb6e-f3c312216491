<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.VideoPlayer" parent="Theme.Material3.DayNight">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary_blue</item>
        <item name="colorPrimaryVariant">@color/primary_blue_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/accent_orange</item>
        <item name="colorSecondaryVariant">@color/accent_orange_light</item>
        <item name="colorOnSecondary">@color/white</item>
        <!-- Background colors -->
        <item name="android:colorBackground">@color/background_primary</item>
        <item name="colorSurface">@color/background_secondary</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">@color/primary_blue_dark</item>
        <item name="android:windowLightStatusBar">false</item>
        <!-- Text colors -->
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
    </style>
    
    <style name="Theme.VideoPlayer.Fullscreen" parent="Theme.VideoPlayer">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
    </style>
</resources>
