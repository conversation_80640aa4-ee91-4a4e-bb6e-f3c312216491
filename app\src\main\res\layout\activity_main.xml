<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    tools:context=".MainActivity">

    <!-- Header with gradient background -->
    <RelativeLayout
        android:id="@+id/layoutHeader"
        android:layout_width="match_parent"
        android:layout_height="160dp"
        android:background="@drawable/header_gradient"
        android:padding="20dp"
        android:elevation="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">



        <!-- Search Bar -->
        <androidx.appcompat.widget.SearchView
            android:id="@+id/searchView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/search_background"
            android:elevation="4dp"
            android:layout_centerInParent="true"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            app:iconifiedByDefault="false"
            app:queryHint="@string/search_videos"
            app:searchIcon="@drawable/ic_search"
            app:closeIcon="@drawable/ic_clear" />

    </RelativeLayout>

    <!-- Video List with Custom Scrollbar -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/layoutHeader">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerViewVideos"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="16dp"
            android:clipToPadding="false"
            android:scrollbars="none"
            tools:listitem="@layout/item_video" />

        <!-- Custom Scrollbar -->
        <include
            android:id="@+id/scrollbar_indicator"
            layout="@layout/scrollbar_indicator"
            android:layout_width="6dp"
            android:layout_height="match_parent"
            android:layout_gravity="end"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp"
            android:layout_marginEnd="8dp"
            android:visibility="gone" />

    </FrameLayout>

    <!-- Empty State -->
    <LinearLayout
        android:id="@+id/layoutEmptyState"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/layoutHeader">

        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:src="@drawable/ic_video_player"
            android:alpha="0.3"
            app:tint="@color/text_secondary" />

        <TextView
            android:id="@+id/textViewNoVideos"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/no_videos_found"
            android:textSize="18sp"
            android:textColor="@color/text_secondary"
            android:fontFamily="sans-serif-medium"
            android:gravity="center" />

    </LinearLayout>

    <!-- Loading State -->
    <LinearLayout
        android:id="@+id/layoutLoading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/layoutHeader">

        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:indeterminateTint="@color/primary_blue" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/loading"
            android:textSize="16sp"
            android:textColor="@color/text_secondary"
            android:fontFamily="sans-serif-medium" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
